// Simple test file to verify pricing logic
import { 
  getPackagePrice, 
  getPackageOriginalPrice, 
  hasPackageDiscount,
  getPackageDiscountPercentage,
  getProductPrice,
  getProductOriginalPrice,
  hasProductPricing,
  getCheapestPackage
} from './pricing'
import type { Product, Package } from '../types'

// Test data
const testPackage: Package = {
  id: '1',
  name: 'Test Package',
  original_price: 10,
  user_price: 20,
  discount_price: 15,
  distributor_price: 12,
  image: '',
  hasDigitalCodes: false
}

const testProduct: Product = {
  id: '1',
  slug: 'test-product',
  title: 'Test Product',
  description: 'Test Description',
  coverImage: '',
  category: 'test',
  tags: [],
  rating: 5,
  commentCount: 0,
  packages: [testPackage],
  featured: false,
  original_price: 5,
  user_price: 15,
  discount_price: 12,
  distributor_price: 10
}

// Test functions (these would normally be run with a test framework)
console.log('Testing pricing utilities...')

// Test package pricing
console.log('Package pricing tests:')
console.log('User price (with discount):', getPackagePrice(testPackage, 'user')) // Should be 15
console.log('User original price:', getPackageOriginalPrice(testPackage, 'user')) // Should be 20
console.log('Distributor price:', getPackagePrice(testPackage, 'distributor')) // Should be 12
console.log('Distributor original price:', getPackageOriginalPrice(testPackage, 'distributor')) // Should be 20
console.log('Has user discount:', hasPackageDiscount(testPackage, 'user')) // Should be true
console.log('User discount percentage:', getPackageDiscountPercentage(testPackage, 'user')) // Should be 25%

// Test product pricing (no packages)
const productWithoutPackages: Product = { ...testProduct, packages: [] }
console.log('\nProduct pricing tests (no packages):')
console.log('User price (with discount):', getProductPrice(productWithoutPackages, 'user')) // Should be 12
console.log('User original price:', getProductOriginalPrice(productWithoutPackages, 'user')) // Should be 15
console.log('Distributor price:', getProductPrice(productWithoutPackages, 'distributor')) // Should be 10
console.log('Has pricing:', hasProductPricing(productWithoutPackages)) // Should be true

// Test cheapest package
const packages: Package[] = [
  { ...testPackage, id: '1', user_price: 20, discount_price: 15 },
  { ...testPackage, id: '2', user_price: 25, discount_price: 18 },
  { ...testPackage, id: '3', user_price: 30, discount_price: 22 }
]
const productWithPackages: Product = { ...testProduct, packages }
console.log('\nCheapest package test:')
const cheapest = getCheapestPackage(productWithPackages, 'user')
console.log('Cheapest package ID:', cheapest?.id) // Should be '1'
console.log('Cheapest package price:', cheapest ? getPackagePrice(cheapest, 'user') : 'N/A') // Should be 15

console.log('\nAll tests completed!')
