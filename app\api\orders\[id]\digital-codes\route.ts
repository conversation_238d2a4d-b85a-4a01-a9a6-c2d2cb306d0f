import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../../lib/supabase/server'

// Rate limiting
const rateLimit = (() => {
  const requests = new Map()
  return (identifier: string, limit: number) => {
    const now = Date.now()
    const windowStart = now - 60000 // 1 minute window
    
    if (!requests.has(identifier)) {
      requests.set(identifier, [])
    }
    
    const userRequests = requests.get(identifier)
    const recentRequests = userRequests.filter((time: number) => time > windowStart)
    
    if (recentRequests.length >= limit) {
      return false
    }
    
    recentRequests.push(now)
    requests.set(identifier, recentRequests)
    return true
  }
})()

// GET /api/orders/[id]/digital-codes - Get digital codes for a specific order
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 20)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's tenant
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('tenant_id, name, email')
      .eq('id', user.id)
      .single()

    if (!profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    // Verify the order belongs to the user and is in their tenant
    const { data: order } = await supabase
      .from('orders')
      .select('id, user_id, status, tenant_id')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 })
    }

    // Only allow access to digital codes for completed orders
    if (order.status !== 'completed') {
      return NextResponse.json({ error: 'Digital codes only available for completed orders' }, { status: 403 })
    }

    // Get digital codes assigned to this order
    const { data: codes, error } = await supabase
      .from('digital_codes')
      .select(`
        id,
        key_encrypted,
        used,
        viewed_count,
        last_viewed_at,
        assigned_at
      `)
      .eq('assigned_to_order_id', params.id)
      .eq('tenant_id', profile.tenant_id)

    if (error) {
      console.error('Error fetching digital codes:', error)
      return NextResponse.json({ error: 'Failed to fetch digital codes' }, { status: 500 })
    }

    // Update viewed count for each code
    if (codes && codes.length > 0) {
      const codeIds = codes.map(code => code.id)
      await supabase
        .from('digital_codes')
        .update({ 
          viewed_count: supabase.raw('viewed_count + 1'),
          last_viewed_at: new Date().toISOString()
        })
        .in('id', codeIds)
    }

    return NextResponse.json({
      success: true,
      codes: codes || []
    })

  } catch (error) {
    console.error('Error in GET /api/orders/[id]/digital-codes:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
