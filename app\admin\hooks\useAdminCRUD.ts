import { useState, useCallback, useEffect } from 'react'
import { toast } from 'sonner'

export interface PaginationData {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface CRUDConfig {
  entityType: 'products' | 'categories'
  apiEndpoint: string
  cacheKey: string
}

interface CacheEntry<T> {
  data: T[]
  pagination: PaginationData
  timestamp: number
  filters: Record<string, any>
}

const CACHE_DURATION = 2 * 60 * 1000 // 2 minutes

export function useAdminCRUD<T extends { id: string }>(config: CRUDConfig) {
  const [items, setItems] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })
  const [filters, setFilters] = useState<Record<string, any>>({})
  const [cache, setCache] = useState<Map<string, CacheEntry<T>>>(new Map())

  // Generate cache key based on current state
  const getCacheKey = useCallback((page: number, currentFilters: Record<string, any>) => {
    const filterString = Object.entries(currentFilters)
      .filter(([_, value]) => value !== '' && value !== null && value !== undefined)
      .map(([key, value]) => `${key}:${value}`)
      .sort()
      .join('|')
    return `${config.cacheKey}-${page}-${filterString}`
  }, [config.cacheKey])

  // Check if cache entry is valid
  const isCacheValid = useCallback((entry: CacheEntry<T>) => {
    return Date.now() - entry.timestamp < CACHE_DURATION
  }, [])

  // Fetch items with caching
  const fetchItems = useCallback(async (
    page: number = 1, 
    newFilters: Record<string, any> = filters,
    useCache: boolean = true
  ) => {
    const cacheKey = getCacheKey(page, newFilters)
    
    // Check cache first
    if (useCache) {
      const cachedEntry = cache.get(cacheKey)
      if (cachedEntry && isCacheValid(cachedEntry)) {
        setItems(cachedEntry.data)
        setPagination(cachedEntry.pagination)
        setLoading(false)
        return
      }
    }

    try {
      setLoading(true)
      
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        ...Object.entries(newFilters)
          .filter(([_, value]) => value !== '' && value !== null && value !== undefined)
          .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {})
      })

      const response = await fetch(`${config.apiEndpoint}?${params}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch data')
      }

      const newItems = data[config.entityType] || []
      const newPagination = data.pagination || pagination

      setItems(newItems)
      setPagination(newPagination)
      
      // Update cache
      setCache(prev => {
        const newCache = new Map(prev)
        newCache.set(cacheKey, {
          data: newItems,
          pagination: newPagination,
          timestamp: Date.now(),
          filters: newFilters
        })
        return newCache
      })

    } catch (error) {
      console.error(`Error fetching ${config.entityType}:`, error)
      toast.error(`فشل في تحميل ${config.entityType === 'products' ? 'المنتجات' : 'الفئات'}`)
    } finally {
      setLoading(false)
    }
  }, [config, pagination.limit, filters, cache, getCacheKey, isCacheValid])

  // Create item
  const createItem = useCallback(async (data: Partial<T>) => {
    try {
      setActionLoading('create')
      
      const response = await fetch(config.apiEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to create item')
      }

      toast.success(result.message || `تم إنشاء ${config.entityType === 'products' ? 'المنتج' : 'الفئة'} بنجاح`)
      
      // Clear cache and refresh
      setCache(new Map())
      await fetchItems(1, filters, false)
      
      return { success: true, data: result[config.entityType.slice(0, -1)] }

    } catch (error) {
      console.error(`Error creating ${config.entityType.slice(0, -1)}:`, error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      toast.error(`فشل في إنشاء ${config.entityType === 'products' ? 'المنتج' : 'الفئة'}: ${errorMessage}`)
      return { success: false, error: errorMessage }
    } finally {
      setActionLoading(null)
    }
  }, [config, fetchItems, filters])

  // Update item
  const updateItem = useCallback(async (id: string, data: Partial<T>) => {
    try {
      setActionLoading(id)
      
      const response = await fetch(`${config.apiEndpoint}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to update item')
      }

      toast.success(result.message || `تم تحديث ${config.entityType === 'products' ? 'المنتج' : 'الفئة'} بنجاح`)
      
      // Clear cache and refresh
      setCache(new Map())
      await fetchItems(pagination.page, filters, false)
      
      return { success: true, data: result[config.entityType.slice(0, -1)] }

    } catch (error) {
      console.error(`Error updating ${config.entityType.slice(0, -1)}:`, error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      toast.error(`فشل في تحديث ${config.entityType === 'products' ? 'المنتج' : 'الفئة'}: ${errorMessage}`)
      return { success: false, error: errorMessage }
    } finally {
      setActionLoading(null)
    }
  }, [config, fetchItems, pagination.page, filters])

  // Delete item
  const deleteItem = useCallback(async (id: string) => {
    if (!confirm(`هل أنت متأكد من حذف هذا ${config.entityType === 'products' ? 'المنتج' : 'الفئة'}؟`)) {
      return { success: false, error: 'Cancelled by user' }
    }

    try {
      setActionLoading(id)
      
      const response = await fetch(`${config.apiEndpoint}/${id}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete item')
      }

      toast.success(result.message || `تم حذف ${config.entityType === 'products' ? 'المنتج' : 'الفئة'} بنجاح`)
      
      // Clear cache and refresh
      setCache(new Map())
      await fetchItems(pagination.page, filters, false)
      
      return { success: true }

    } catch (error) {
      console.error(`Error deleting ${config.entityType.slice(0, -1)}:`, error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      toast.error(`فشل في حذف ${config.entityType === 'products' ? 'المنتج' : 'الفئة'}: ${errorMessage}`)
      return { success: false, error: errorMessage }
    } finally {
      setActionLoading(null)
    }
  }, [config, fetchItems, pagination.page, filters])

  // Pagination helpers
  const goToPage = useCallback((page: number) => {
    fetchItems(page, filters)
  }, [fetchItems, filters])

  const nextPage = useCallback(() => {
    if (pagination.hasNext) {
      goToPage(pagination.page + 1)
    }
  }, [pagination, goToPage])

  const prevPage = useCallback(() => {
    if (pagination.hasPrev) {
      goToPage(pagination.page - 1)
    }
  }, [pagination, goToPage])

  // Filter helpers
  const updateFilters = useCallback((newFilters: Record<string, any>) => {
    setFilters(newFilters)
    fetchItems(1, newFilters, false) // Reset to page 1 with new filters
  }, [fetchItems])

  // Initial load
  useEffect(() => {
    fetchItems(1, {}, true)
  }, []) // Only run on mount

  return {
    // Data
    items,
    loading,
    actionLoading,
    pagination,
    filters,

    // Actions
    fetchItems,
    createItem,
    updateItem,
    deleteItem,

    // Pagination
    goToPage,
    nextPage,
    prevPage,

    // Filters
    updateFilters,
    setFilters
  }
}
