"use client"

import { useState, useEffect } from "react"
import { Search, Filter, Calendar, Package, Eye, Download, Copy, Check, Clock, CheckCircle, XCircle, Key, EyeOff, ChevronLeft, ChevronRight } from "lucide-react"
import { useAuth } from "../contexts/AuthContext"
import { useData } from "../contexts/DataContext"
import { toast } from "sonner"
import { getCurrencySymbol } from "../utils/currency"
import Pagination from "../components/Pagination"

interface OrderWithDetails {
  id: string
  amount: number
  status: 'pending' | 'completed' | 'failed'
  custom_data: Record<string, any>
  created_at: string
  updated_at: string
  products: {
    id: string
    title: string
    slug: string
    cover_image: string
  }
  packages: {
    id: string
    name: string
    price: number
    image: string
  }
  digitalCodes?: {
    id: string
    key_encrypted: string
    used: boolean
    viewed_count: number
    last_viewed_at: string
  }[]
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export default function OrdersPage() {
  const { authState } = useAuth()
  const { currencies, selectedCurrency } = useData()
  const authUser = authState.user

  // State management
  const [orders, setOrders] = useState<OrderWithDetails[]>([])
  const [filteredOrders, setFilteredOrders] = useState<OrderWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | "pending" | "completed" | "failed">("all")
  const [dateFilter, setDateFilter] = useState<"all" | "today" | "week" | "month">("all")
  const [selectedOrder, setSelectedOrder] = useState<OrderWithDetails | null>(null)
  const [showOrderDetails, setShowOrderDetails] = useState(false)
  const [visibleCodes, setVisibleCodes] = useState<Set<string>>(new Set())
  const [copiedCodes, setCopiedCodes] = useState<Set<string>>(new Set())

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })

  // Load orders from API with pagination
  const loadOrders = async (page: number = 1) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/orders?page=${page}&limit=10`)

      if (!response.ok) {
        throw new Error('Failed to fetch orders')
      }

      const data = await response.json()
      setOrders(data.orders || [])
      setPagination(data.pagination || {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      })
    } catch (error) {
      console.error('Error loading orders:', error)
      toast.error('فشل في تحميل الطلبات')
    } finally {
      setLoading(false)
    }
  }

  // Load digital codes for an order
  const loadDigitalCodes = async (orderId: string) => {
    try {
      const response = await fetch(`/api/orders/${orderId}/digital-codes`)
      if (response.ok) {
        const data = await response.json()
        return data.codes || []
      }
    } catch (error) {
      console.error('Error loading digital codes:', error)
    }
    return []
  }

  // Note: Filtering is now handled server-side with pagination
  // For now, we'll keep client-side filtering for the current page only
  useEffect(() => {
    let filtered = [...orders]

    // Simple client-side filtering for current page
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.products.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.packages.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.id.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    setFilteredOrders(filtered)
  }, [orders, searchTerm, statusFilter])

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    loadOrders(page)
  }

  // Load orders on component mount
  useEffect(() => {
    if (authUser) {
      loadOrders(currentPage)
    }
  }, [authUser])

  // Update filtered orders when orders change (for client-side filtering)
  useEffect(() => {
    setFilteredOrders(orders)
  }, [orders])

  // Authentication check - moved after hooks
  if (!authUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">يجب تسجيل الدخول أولاً</h1>
          <p className="text-gray-400">يرجى تسجيل الدخول للوصول إلى طلباتك</p>
        </div>
      </div>
    )
  }

  // Status styling helpers
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'failed':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'failed':
        return <XCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  // Handle order details view
  const handleViewOrderDetails = async (order: OrderWithDetails) => {
    const codes = await loadDigitalCodes(order.id)
    setSelectedOrder({ ...order, digitalCodes: codes })
    setShowOrderDetails(true)
  }

  // Handle digital code visibility toggle
  const toggleCodeVisibility = (codeId: string) => {
    setVisibleCodes(prev => {
      const newSet = new Set(prev)
      if (newSet.has(codeId)) {
        newSet.delete(codeId)
      } else {
        newSet.add(codeId)
      }
      return newSet
    })
  }

  // Handle code copying
  const copyCode = async (code: string, codeId: string) => {
    try {
      await navigator.clipboard.writeText(code)
      setCopiedCodes(prev => new Set(prev).add(codeId))
      toast.success('تم نسخ الكود بنجاح')

      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopiedCodes(prev => {
          const newSet = new Set(prev)
          newSet.delete(codeId)
          return newSet
        })
      }, 2000)
    } catch (error) {
      toast.error('فشل في نسخ الكود')
    }
  }

  // Format order amount in original purchase currency (not current selected currency)
  const formatOrderAmount = (order: OrderWithDetails) => {
    const originalAmount = order.custom_data?.amount_in_currency || order.amount
    const originalCurrency = order.custom_data?.currency_code || 'USD'
    const currencySymbol = getCurrencySymbol(originalCurrency as any)

    return `${originalAmount.toFixed(2)} ${currencySymbol}`
  }

  return (
    <div className="container mx-auto px-4 py-4 sm:py-8">
      {/* Mobile-Optimized Header */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2 sm:mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
          طلباتي
        </h1>
        <p className="text-gray-400 text-sm sm:text-lg">عرض وإدارة تاريخ طلباتك والأكواد الرقمية</p>

        {/* Orders Summary */}
        {!loading && (
          <div className="mt-3 sm:mt-4 flex items-center gap-4 text-sm text-gray-400">
            <span>إجمالي الطلبات: {pagination.total}</span>
            <span>الصفحة {pagination.page} من {pagination.totalPages}</span>
          </div>
        )}
      </div>

      {/* Mobile-Optimized Filters and Search */}
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 sm:p-6 border border-gray-700/50 shadow-xl mb-6 sm:mb-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="البحث في الطلبات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg pr-10 pl-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          {/* Status Filter */}
          <div className="relative">
            <Filter className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg pr-10 pl-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent appearance-none"
            >
              <option value="all">جميع الحالات</option>
              <option value="completed">مكتملة</option>
              <option value="pending">قيد الانتظار</option>
              <option value="failed">فاشلة</option>
            </select>
          </div>

          {/* Date Filter */}
          <div className="relative">
            <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value as any)}
              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg pr-10 pl-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent appearance-none"
            >
              <option value="all">جميع التواريخ</option>
              <option value="today">اليوم</option>
              <option value="week">هذا الأسبوع</option>
              <option value="month">هذا الشهر</option>
            </select>
          </div>

          {/* Results Count */}
          <div className="flex items-center justify-center bg-gray-700/30 rounded-lg px-4 py-3">
            <Package className="w-5 h-5 text-purple-400 ml-2" />
            <span className="text-white font-semibold">{filteredOrders.length} طلب</span>
          </div>
        </div>
      </div>

      {/* Orders List */}
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
            <span className="mr-3 text-gray-400">جاري تحميل الطلبات...</span>
          </div>
        ) : filteredOrders.length > 0 ? (
          <>
            <div className="divide-y divide-gray-700/50">
              {filteredOrders.map((order) => {
                const quantity = order.custom_data?.quantity || 1

                return (
                  <div key={order.id} className="p-4 sm:p-6 hover:bg-gray-700/20 transition-all duration-300">
                    {/* Mobile-First Compact Design */}
                    <div className="flex items-center gap-3 sm:gap-4">
                      {/* Product Image - Smaller on mobile */}
                      <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-lg overflow-hidden bg-gray-700/50 flex-shrink-0">
                        <img
                          src={order.products.cover_image}
                          alt={order.products.title}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.src = '/placeholder-product.png'
                          }}
                        />
                      </div>

                      {/* Order Info - Compact Layout */}
                      <div className="flex-1 min-w-0">
                        {/* Order ID and Status - Top Row */}
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs sm:text-sm font-mono text-gray-400">
                            #{order.id.slice(0, 8)}
                          </span>
                          <div className={`inline-flex items-center space-x-1 space-x-reverse px-2 py-1 rounded-full text-xs border ${getStatusColor(order.status)}`}>
                            {getStatusIcon(order.status)}
                            <span className="hidden sm:inline">
                              {order.status === "completed" ? "مكتمل" :
                               order.status === "pending" ? "قيد الانتظار" : "فاشل"}
                            </span>
                          </div>
                        </div>

                        {/* Product Title - Truncated */}
                        <h3 className="text-sm sm:text-base font-semibold text-white truncate mb-1">
                          {order.products.title}
                        </h3>

                        {/* Package and Date - Bottom Row */}
                        <div className="flex items-center justify-between text-xs sm:text-sm text-gray-400">
                          <span className="truncate">{order.packages.name}</span>
                          <span className="ml-2 flex-shrink-0">
                            {new Date(order.created_at).toLocaleDateString("ar-SA", {
                              month: 'short',
                              day: 'numeric'
                            })}
                          </span>
                        </div>

                        {/* Amount and View Button */}
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-sm sm:text-base font-bold text-purple-400">
                            {formatOrderAmount(order)}
                          </span>
                          <button
                            onClick={() => {
                              setSelectedOrder(order)
                              setShowOrderDetails(true)
                            }}
                            className="flex items-center space-x-1 space-x-reverse bg-purple-600/20 hover:bg-purple-600/30 text-purple-400 px-3 py-1.5 rounded-lg transition-all duration-200 text-xs sm:text-sm"
                          >
                            <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
                            <span className="hidden sm:inline">عرض التفاصيل</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="p-4 sm:p-6 border-t border-gray-700/50">
                <Pagination
                  currentPage={pagination.page}
                  totalPages={pagination.totalPages}
                  onPageChange={handlePageChange}
                  className="justify-center"
                />
              </div>
            )}
          </>

        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📦</div>
            <h3 className="text-xl font-semibold mb-2">لا توجد طلبات</h3>
            <p className="text-gray-400 mb-6">
              {searchTerm || statusFilter !== 'all' || dateFilter !== 'all'
                ? 'لم يتم العثور على طلبات تطابق المعايير المحددة'
                : 'لم تقم بأي طلبات بعد'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && dateFilter === 'all' && (
              <button
                onClick={() => window.location.href = '/shop'}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300"
              >
                تصفح المنتجات
              </button>
            )}
          </div>
        )}
      </div>

      {/* Enhanced Order Details Modal */}
      {showOrderDetails && selectedOrder && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-end sm:items-center justify-center p-0 sm:p-4 z-50">
          <div className="bg-gray-800 rounded-t-xl sm:rounded-xl max-w-4xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
            {/* Modal Header - Mobile Optimized */}
            <div className="sticky top-0 bg-gray-800 flex items-center justify-between p-4 sm:p-6 border-b border-gray-700 z-10">
              <h2 className="text-lg sm:text-2xl font-bold text-white">تفاصيل الطلب</h2>
              <button
                onClick={() => setShowOrderDetails(false)}
                className="text-gray-400 hover:text-white transition-colors p-1"
              >
                <XCircle className="w-5 h-5 sm:w-6 sm:h-6" />
              </button>
            </div>

            {/* Modal Content - Mobile Optimized */}
            <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
              {/* Product Image and Basic Info */}
              <div className="flex items-start gap-4 bg-gray-700/50 rounded-lg p-4">
                <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden bg-gray-600/50 flex-shrink-0">
                  <img
                    src={selectedOrder.products.cover_image}
                    alt={selectedOrder.products.title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = '/placeholder-product.png'
                    }}
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg sm:text-xl font-semibold text-white mb-1 truncate">
                    {selectedOrder.products.title}
                  </h3>
                  <p className="text-gray-300 mb-2">{selectedOrder.packages.name}</p>
                  <div className="flex items-center justify-between">
                    <div className={`inline-flex items-center space-x-1 space-x-reverse px-2 py-1 rounded-full text-xs sm:text-sm border ${getStatusColor(selectedOrder.status)}`}>
                      {getStatusIcon(selectedOrder.status)}
                      <span>
                        {selectedOrder.status === "completed" ? "مكتمل" :
                         selectedOrder.status === "pending" ? "قيد الانتظار" : "فاشل"}
                      </span>
                    </div>
                    <div className="text-left">
                      <div className="text-lg sm:text-xl font-bold text-purple-400">
                        {formatOrderAmount(selectedOrder)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Order Details Grid */}
              <div className="bg-gray-700/50 rounded-lg p-4">
                <h3 className="text-base sm:text-lg font-semibold text-white mb-4">تفاصيل الطلب</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-start">
                    <span className="text-gray-400 text-sm">رقم الطلب</span>
                    <span className="text-white font-mono text-sm break-all">{selectedOrder.id}</span>
                  </div>
                  <div className="flex justify-between items-start">
                    <span className="text-gray-400 text-sm">تاريخ الطلب</span>
                    <span className="text-white text-sm">{new Date(selectedOrder.created_at).toLocaleDateString('ar-SA')}</span>
                  </div>
                  <div className="flex justify-between items-start">
                    <span className="text-gray-400 text-sm">الكمية</span>
                    <span className="text-white text-sm">{selectedOrder.custom_data?.quantity || 1}</span>
                  </div>
                  <div className="flex justify-between items-start">
                    <span className="text-gray-400 text-sm">طريقة الدفع</span>
                    <span className="text-white text-sm">{selectedOrder.custom_data?.payment_method === 'wallet' ? 'محفظة' : 'خارجي'}</span>
                  </div>
                </div>
              </div>

              {/* Custom Data */}
              {selectedOrder.custom_data && Object.keys(selectedOrder.custom_data).length > 0 && (
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <h3 className="text-base sm:text-lg font-semibold text-white mb-4">البيانات الإضافية</h3>
                  <div className="space-y-3">
                    {Object.entries(selectedOrder.custom_data)
                      .filter(([key, value]) =>
                        !['quantity', 'currency_code', 'amount_in_currency', 'payment_method'].includes(key) &&
                        value !== null && value !== undefined && value !== ''
                      )
                      .map(([key, value]) => (
                        <div key={key} className="flex justify-between items-start">
                          <span className="text-gray-400 text-sm capitalize">{key.replace(/_/g, ' ')}</span>
                          <span className="text-white text-sm break-all max-w-[60%] text-left">{String(value)}</span>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {/* Digital Codes - Mobile Optimized */}
              {selectedOrder.digitalCodes && selectedOrder.digitalCodes.length > 0 && (
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <h3 className="text-base sm:text-lg font-semibold text-white mb-4 flex items-center">
                    <Key className="w-4 h-4 sm:w-5 sm:h-5 ml-2" />
                    الأكواد الرقمية
                  </h3>
                  <div className="space-y-3">
                    {selectedOrder.digitalCodes.map((code) => (
                      <div key={code.id} className="bg-gray-600/50 rounded-lg p-3 sm:p-4">
                        <div className="space-y-3">
                          {/* Code Display */}
                          <div className="flex items-center gap-2">
                            <span className="text-white font-mono text-sm flex-1 break-all">
                              {visibleCodes.has(code.id) ? code.key_encrypted : '••••••••••••••••'}
                            </span>
                            <button
                              onClick={() => toggleCodeVisibility(code.id)}
                              className="text-gray-400 hover:text-white transition-colors p-1"
                            >
                              {visibleCodes.has(code.id) ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                            </button>
                          </div>

                          {/* Code Info and Actions */}
                          <div className="flex items-center justify-between">
                            <div className="text-xs text-gray-400">
                              <div>مرات المشاهدة: {code.viewed_count}</div>
                              {code.last_viewed_at && (
                                <div>آخر مشاهدة: {new Date(code.last_viewed_at).toLocaleDateString('ar-SA')}</div>
                              )}
                            </div>
                            <button
                              onClick={() => copyCode(code.key_encrypted, code.id)}
                              className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-xs sm:text-sm font-semibold transition-all duration-300 flex items-center space-x-1 space-x-reverse"
                            >
                              {copiedCodes.has(code.id) ? <Check className="w-3 h-3 sm:w-4 sm:h-4" /> : <Copy className="w-3 h-3 sm:w-4 sm:h-4" />}
                              <span>{copiedCodes.has(code.id) ? 'تم النسخ' : 'نسخ'}</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Modal Footer - Mobile Optimized */}
            <div className="sticky bottom-0 bg-gray-800 flex items-center justify-center p-4 sm:p-6 border-t border-gray-700">
              <button
                onClick={() => setShowOrderDetails(false)}
                className="w-full sm:w-auto bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
